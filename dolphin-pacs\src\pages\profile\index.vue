<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/pinia/stores/user'
import { getResourceApi } from '@/common/apis/resources/index'
import { uploadAvatarApi, validateFile, DEFAULT_FILE_CONFIG } from '@/common/apis/files/index'
import { changePasswordApi } from './apis'

const router = useRouter()
const userStore = useUserStore()

// 个人信息表单数据
const profileForm = ref({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  avatar: ''
})

// 头像blob URL
const avatarBlobUrl = ref<string>('')

// 获取头像图片
const loadAvatar = async (avatarPath: string) => {
  try {
    console.log('🔄 Loading avatar:', avatarPath)

    // 调用资源API获取图片blob
    const blob = await getResourceApi(avatarPath)
    console.log('✅ Avatar blob received:', blob)

    // 创建blob URL
    const blobUrl = URL.createObjectURL(blob as Blob)
    console.log('🎯 Avatar blob URL created:', blobUrl)

    // 清理之前的blob URL
    if (avatarBlobUrl.value) {
      URL.revokeObjectURL(avatarBlobUrl.value)
    }

    avatarBlobUrl.value = blobUrl
  } catch (error) {
    console.error('❌ Failed to load avatar:', error)
    avatarBlobUrl.value = ''
  }
}

// 计算头像URL
const avatarUrl = computed(() => {
  return avatarBlobUrl.value || ''
})

// 初始化用户信息
const initUserInfo = () => {
  console.log('Initializing user info:', userStore.userInfo)
  if (userStore.userInfo) {
    profileForm.value = {
      username: userStore.userInfo.username || '',
      nickname: userStore.userInfo.nickname || '',
      email: userStore.userInfo.email || '',
      phone: userStore.userInfo.phone || '',
      avatar: userStore.userInfo.avatar || ''
    }
    console.log('Profile form updated:', profileForm.value)

    // 如果有头像路径，加载头像
    if (userStore.userInfo.avatar) {
      loadAvatar(userStore.userInfo.avatar)
    }
  }
}

// 监听用户信息变化
watch(() => userStore.userInfo, (newUserInfo: any) => {
  if (newUserInfo) {
    initUserInfo()
  }
}, { immediate: true })

// 组件挂载时初始化数据
onMounted(() => {
  initUserInfo()
})

// 密码修改表单数据
const passwordForm = ref({
  password: ''
})

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: false, type: 'email' as const, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 5, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 保存个人信息
const saveProfile = () => {
  console.log('保存个人信息:', profileForm.value)
  ElMessage.success('个人信息保存成功')
}

// 修改密码
const changePassword = async () => {
  try {
    console.log('修改密码:', passwordForm.value)

    // 调用修改密码API
    const response = await changePasswordApi({
      password: passwordForm.value.password
    })

    console.log('密码修改成功:', response)
    ElMessage.success('密码修改成功，请重新登录')

    // 重置密码表单
    passwordForm.value = {
      password: ''
    }

    // 清除本地token并跳转到登录页
    setTimeout(() => {
      userStore.logout()
      router.push('/login')
    }, 1500) // 延迟1.5秒让用户看到成功消息

  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error('密码修改失败，请重试')
  }
}

// 上传头像
const handleAvatarUpload = async (file: File) => {
  try {
    console.log('🔄 开始上传头像:', file.name)

    // 文件验证
    const validation = validateFile(file, DEFAULT_FILE_CONFIG.avatar)
    if (!validation.valid) {
      ElMessage.error(validation.message || '文件验证失败')
      return false
    }

    // 显示上传进度
    const loadingMessage = ElMessage({
      message: '正在上传头像...',
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: false
    })

    // 调用上传接口
    if (!userStore.userInfo?.id) {
      throw new Error('用户信息不完整，无法上传头像')
    }

    const response = await uploadAvatarApi(file, userStore.userInfo.id)
    console.log('✅ 头像上传成功:', response)

    // 关闭加载消息
    loadingMessage.close()

    // 更新用户头像信息
    if (response.data) {
      // response.data 直接是文件路径字符串
      const avatarPath = response.data
      profileForm.value.avatar = avatarPath

      // 重新加载头像显示
      await loadAvatar(avatarPath)

      // 更新用户store中的头像信息
      if (userStore.userInfo) {
        userStore.userInfo.avatar = avatarPath
      }

      ElMessage.success('头像上传成功!')
    } else {
      throw new Error('上传响应数据格式错误')
    }

  } catch (error) {
    console.error('❌ 头像上传失败:', error)

    // 显示错误信息
    const errorMessage = error instanceof Error ? error.message : '头像上传失败，请重试'
    ElMessage.error(errorMessage)
  }

  // 阻止Element Plus的默认上传行为
  return false
}
</script>

<template>
  <div class="profile-container">
    <div class="profile-content">
      <el-row :gutter="24">
        <!-- 左侧个人信息 -->
        <el-col :span="16">
          <el-card class="profile-card">
            <template #header>
              <div class="card-header">
                <span>个人信息</span>
              </div>
            </template>
            
            <el-form
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
              class="profile-form"
            >
              <el-form-item label="用户名" prop="username">
                <el-input v-model="profileForm.username" placeholder="请输入用户名" />
              </el-form-item>

              <el-form-item label="昵称">
                <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" placeholder="请输入邮箱地址" />
              </el-form-item>

              <el-form-item label="手机号">
                <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveProfile">保存信息</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 密码修改 -->
          <el-card class="profile-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>修改密码</span>
              </div>
            </template>
            
            <el-form
              :model="passwordForm"
              :rules="passwordRules"
              label-width="100px"
              class="password-form"
            >
              <el-form-item label="新密码" prop="password">
                <el-input
                  v-model="passwordForm.password"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="changePassword">修改密码</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 右侧头像和基本信息 -->
        <el-col :span="8">
          <el-card class="avatar-card">
            <div class="avatar-section">
              <el-avatar :size="120" :src="avatarUrl" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>

              <div class="avatar-info">
                <h3>{{ profileForm.nickname || profileForm.username || '用户名' }}</h3>
                <p>{{ profileForm.username }}</p>
              </div>
              
              <el-upload
                :before-upload="handleAvatarUpload"
                :show-file-list="false"
                accept=".jpg,.jpeg,.png,.gif,.webp"
                :limit="1"
                class="avatar-upload"
              >
                <el-button type="primary" size="small">
                  <el-icon><Upload /></el-icon>
                  更换头像
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 2MB
                  </div>
                </template>
              </el-upload>
            </div>
          </el-card>

        
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100%;
}

.profile-content {
  .profile-card {
    .card-header {
      font-weight: 600;
      color: #303133;
    }
  }
}

.avatar-card {
  .avatar-section {
    text-align: center;
    
    .user-avatar {
      margin-bottom: 16px;
      border: 4px solid #f0f0f0;
    }
    
    .avatar-info {
      margin-bottom: 20px;
      
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      
      p {
        margin: 4px 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.stats-card {
  .stats-content {
    display: flex;
    justify-content: space-around;
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.profile-form,
.password-form {
  max-width: 500px;
}
</style>
