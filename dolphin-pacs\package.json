{"name": "dolphin-pacs", "type": "module", "version": "5.0.2", "scripts": {"dev": "vite", "build:staging": "vue-tsc && vite build --mode staging", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "prepare": "husky", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.10.0", "dayjs": "1.11.13", "element-plus": "2.10.4", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.3", "screenfull": "6.0.2", "vue": "3.5.17", "vue-router": "4.5.1", "vxe-table": "4.6.25"}, "devDependencies": {"@antfu/eslint-config": "4.17.0", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "24.0.14", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@vitejs/plugin-vue": "6.0.0", "@vue/test-utils": "2.4.6", "eslint": "9.31.0", "eslint-plugin-format": "1.0.1", "happy-dom": "18.0.1", "husky": "9.1.7", "lint-staged": "16.1.2", "sass-embedded": "1.78.0", "typescript": "5.8.3", "unocss": "66.3.3", "unplugin-auto-import": "19.3.0", "unplugin-svg-component": "0.12.2", "unplugin-vue-components": "28.8.0", "vite": "6.0.4", "vite-plugin-vue-mcp": "0.3.2", "vite-svg-loader": "5.1.0", "vitest": "3.2.4", "vue-tsc": "3.0.1"}, "lint-staged": {"*": "eslint --fix"}}