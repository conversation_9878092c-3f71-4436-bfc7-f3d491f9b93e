import type { PatientInfo, DepartmentOption, StatusOption } from "../types"

/** 科室选项数据 */
export const departmentOptions: DepartmentOption[] = [
  { label: "全部科室", value: "" },
  { label: "放射科", value: "放射科" },
  { label: "超声科", value: "超声科" },
  { label: "核医学科", value: "核医学科" },
  { label: "心电图科", value: "心电图科" },
  { label: "内镜科", value: "内镜科" }
]

/** 状态选项数据 */
export const statusOptions: StatusOption[] = [
  { label: "全部状态", value: "", type: "info" },
  { label: "待检查", value: "待检查", type: "warning" },
  { label: "检查中", value: "检查中", type: "primary" },
  { label: "分析中", value: "分析中", type: "info" },
  { label: "已完成", value: "已完成", type: "success" },
  { label: "已取消", value: "已取消", type: "danger" }
]

/** 模拟患者数据 */
export const mockPatientData: PatientInfo[] = [
  {
    id: 1,
    name: "张三",
    gender: "男",
    age: 39,
    examType: "胸部CT平扫",
    examTime: "2023-05-12 09:30",
    department: "放射科",
    status: "分析中"
  },
  {
    id: 2,
    name: "李四",
    gender: "女",
    age: 45,
    examType: "腹部超声",
    examTime: "2023-05-12 10:15",
    department: "超声科",
    status: "已完成"
  },
  {
    id: 3,
    name: "王五",
    gender: "男",
    age: 28,
    examType: "心脏彩超",
    examTime: "2023-05-12 11:00",
    department: "超声科",
    status: "检查中"
  },
  {
    id: 4,
    name: "赵六",
    gender: "女",
    age: 52,
    examType: "头颅MRI",
    examTime: "2023-05-12 14:20",
    department: "放射科",
    status: "待检查"
  },
  {
    id: 5,
    name: "钱七",
    gender: "男",
    age: 35,
    examType: "骨密度检查",
    examTime: "2023-05-12 15:45",
    department: "核医学科",
    status: "已完成"
  },
  {
    id: 6,
    name: "孙八",
    gender: "女",
    age: 41,
    examType: "乳腺钼靶",
    examTime: "2023-05-12 16:30",
    department: "放射科",
    status: "分析中"
  },
  {
    id: 7,
    name: "周九",
    gender: "男",
    age: 67,
    examType: "心电图",
    examTime: "2023-05-13 08:15",
    department: "心电图科",
    status: "已完成"
  },
  {
    id: 8,
    name: "吴十",
    gender: "女",
    age: 33,
    examType: "胃镜检查",
    examTime: "2023-05-13 09:00",
    department: "内镜科",
    status: "检查中"
  },
  {
    id: 9,
    name: "郑十一",
    gender: "男",
    age: 58,
    examType: "肺部CT",
    examTime: "2023-05-13 10:30",
    department: "放射科",
    status: "待检查"
  },
  {
    id: 10,
    name: "王十二",
    gender: "女",
    age: 29,
    examType: "甲状腺超声",
    examTime: "2023-05-13 11:15",
    department: "超声科",
    status: "已完成"
  },
  {
    id: 11,
    name: "陈十三",
    gender: "男",
    age: 44,
    examType: "腰椎MRI",
    examTime: "2023-05-13 14:00",
    department: "放射科",
    status: "分析中"
  },
  {
    id: 12,
    name: "刘十四",
    gender: "女",
    age: 36,
    examType: "肝胆超声",
    examTime: "2023-05-13 15:30",
    department: "超声科",
    status: "检查中"
  },
  {
    id: 13,
    name: "张十五",
    gender: "男",
    age: 62,
    examType: "冠脉CTA",
    examTime: "2023-05-14 08:45",
    department: "放射科",
    status: "待检查"
  },
  {
    id: 14,
    name: "李十六",
    gender: "女",
    age: 27,
    examType: "肠镜检查",
    examTime: "2023-05-14 09:30",
    department: "内镜科",
    status: "已取消"
  },
  {
    id: 15,
    name: "王十七",
    gender: "男",
    age: 55,
    examType: "骨扫描",
    examTime: "2023-05-14 10:15",
    department: "核医学科",
    status: "已完成"
  }
]

/** 获取状态对应的标签类型 */
export function getStatusTagType(status: string): "primary" | "success" | "warning" | "danger" | "info" {
  const statusOption = statusOptions.find(option => option.value === status)
  return statusOption?.type || "info"
}
